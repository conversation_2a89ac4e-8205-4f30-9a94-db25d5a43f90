identity:
  name: add_mem0ai_memory
  author: moch<PERSON> (v0.3.0 Optimization by mocha, Original by ye<PERSON><PERSON>)
  label:
    en_US: 💾 Add Memory
    zh_Hans: 💾 添加记忆
    pt_BR: 💾 Adici<PERSON><PERSON>
    ja_JP: 💾 メモリ追加
description:
  human:
    en_US: Store conversation memories with AI-powered inference and context extraction
    zh_Hans: 存储对话记忆，支持AI推理和上下文提取
    pt_BR: Armazenar memórias de conversas com inferência de IA e extração de contexto
    ja_JP: AI推論とコンテキスト抽出による会話メモリの保存
  llm: Store conversation memories with AI-powered inference to extract meaningful insights and context from user-assistant interactions
parameters:
  - name: user
    type: string
    required: true
    label:
      en_US: User Message
      zh_Hans: 用户消息
      pt_BR: Mensagem do Usuário
    human_description:
      en_US: The message from the user in the conversation.
      zh_Hans: 对话中的用户消息。
      pt_BR: A mensagem do usuário na conversa.
    llm_description: The message from the user in the conversation.
    form: llm
  - name: assistant
    type: string
    required: true
    label:
      en_US: Assistant Response
      zh_Hans: 助手回应
      pt_BR: Resposta do Assistente
    human_description:
      en_US: The response from the assistant in the conversation.
      zh_Hans: 对话中的助手回应。
      pt_BR: A resposta do assistente na conversa.
    llm_description: The response from the assistant in the conversation.
    form: llm
  - name: user_id
    type: string
    required: true
    label:
      en_US: User ID
      zh_Hans: 用户ID
      pt_BR: ID do Usuário
    human_description:
      en_US: User ID
      zh_Hans: 用户ID
      pt_BR: ID do Usuário
    llm_description: User ID
    form: llm
  - name: agent_id
    type: string
    required: false
    label:
      en_US: Agent ID
      zh_Hans: 代理ID
      pt_BR: ID do Agente
    human_description:
      en_US: Optional agent ID for memory categorization
      zh_Hans: 可选的代理ID用于记忆分类
      pt_BR: ID do agente opcional para categorização de memória
    llm_description: Optional agent ID for memory categorization
    form: llm
  - name: run_id
    type: string
    required: false
    label:
      en_US: Run ID
      zh_Hans: 运行ID
      pt_BR: ID da Execução
    human_description:
      en_US: Optional run ID for session tracking
      zh_Hans: 可选的运行ID用于会话跟踪
      pt_BR: ID de execução opcional para rastreamento de sessão
    llm_description: Optional run ID for session tracking
    form: llm
  - name: metadata
    type: string
    required: false
    label:
      en_US: Metadata (JSON)
      zh_Hans: 元数据 (JSON)
      pt_BR: Metadados (JSON)
    human_description:
      en_US: Optional metadata in JSON format for additional context
      zh_Hans: 可选的JSON格式元数据用于额外上下文
      pt_BR: Metadados opcionais em formato JSON para contexto adicional
    llm_description: Optional metadata in JSON format for additional context
    form: llm
  - name: infer
    type: boolean
    required: false
    default: true
    label:
      en_US: Enable Inference
      zh_Hans: 启用推理
      pt_BR: Habilitar Inferência
    human_description:
      en_US: Enable AI inference to extract meaningful insights from the conversation
      zh_Hans: 启用AI推理从对话中提取有意义的见解
      pt_BR: Habilitar inferência de IA para extrair insights significativos da conversa
    llm_description: Enable AI inference to extract meaningful insights from the conversation
    form: form
  - name: custom_instructions
    type: string
    required: false
    label:
      en_US: Custom Instructions
      zh_Hans: 自定义指令
      pt_BR: Instruções Personalizadas
    human_description:
      en_US: Custom instructions for memory processing and extraction
      zh_Hans: 用于记忆处理和提取的自定义指令
      pt_BR: Instruções personalizadas para processamento e extração de memória
    llm_description: Custom instructions for memory processing and extraction
    form: llm
  - name: custom_categories
    type: string
    required: false
    label:
      en_US: Custom Categories (JSON)
      zh_Hans: 自定义分类 (JSON)
      pt_BR: Categorias Personalizadas (JSON)
    human_description:
      en_US: Custom categories for memory classification in JSON format
      zh_Hans: JSON格式的自定义分类用于记忆分类
      pt_BR: Categorias personalizadas para classificação de memória em formato JSON
    llm_description: Custom categories for memory classification in JSON format
    form: llm
  - name: memory_priority
    type: string
    required: false
    label:
      en_US: Memory Priority
      zh_Hans: 记忆优先级
      pt_BR: Prioridade da Memória
    human_description:
      en_US: Priority level for selective memory management (high, medium, low)
      zh_Hans: 选择性记忆管理的优先级 (high, medium, low)
      pt_BR: Nível de prioridade para gerenciamento seletivo de memória (high, medium, low)
    llm_description: Priority level for selective memory management
    form: llm
  - name: auto_prune
    type: boolean
    required: false
    default: false
    label:
      en_US: Auto Prune
      zh_Hans: 自动清理
      pt_BR: Limpeza Automática
    human_description:
      en_US: Enable automatic pruning of low-priority memories
      zh_Hans: 启用低优先级记忆的自动清理
      pt_BR: Habilitar limpeza automática de memórias de baixa prioridade
    llm_description: Enable automatic pruning of low-priority memories
    form: form
  - name: use_async_client
    type: boolean
    required: false
    default: false
    label:
      en_US: Use Async Client
      zh_Hans: 使用异步客户端
      pt_BR: Usar Cliente Assíncrono
    human_description:
      en_US: Enable asynchronous processing for better performance
      zh_Hans: 启用异步处理以获得更好的性能
      pt_BR: Habilitar processamento assíncrono para melhor desempenho
    llm_description: Enable asynchronous processing for better performance
    form: form
extra:
  python:
    source: tools/add_memory.py
