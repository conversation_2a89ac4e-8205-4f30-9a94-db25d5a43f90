from collections.abc import Generator
from typing import Any
import json
import httpx
from dify_plugin import Tool
from dify_plugin.entities.tool import ToolInvokeMessage

class SearchMemoriesTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage, None, None]:
        # Get API key from credentials
        api_key = self.runtime.credentials["mem0_api_key"]

        # Get API URL from credentials or use default (consistent with provider/mem0.yaml)

        api_url = self.runtime.credentials.get("mem0_api_url", "http://localhost:8000")

        if not api_url or api_url.strip() == "":

            api_url = "http://localhost:8000"

        api_url = api_url.rstrip("/")

        # Get required parameters
        query = tool_parameters.get("query", "")
        user_id = tool_parameters.get("user_id", "")

        # Validate required parameters
        if not query:
            yield self.create_text_message("Error: Query is required.")
            return
        if not user_id:
            yield self.create_text_message("Error: User ID is required.")
            return

        # Prepare V2 payload for search - 使用正确的filters格式
        filters_dict = {}
        
        # Add identity parameters to filters
        if user_id:
            filters_dict["user_id"] = user_id
        if tool_parameters.get("agent_id"):
            filters_dict["agent_id"] = tool_parameters["agent_id"]
        if tool_parameters.get("run_id"):
            filters_dict["run_id"] = tool_parameters["run_id"]
        
        payload = {
            "query": query
        }
        
        # 设置filters字段包含所有标识符
        if filters_dict:
            payload["filters"] = filters_dict

        # Handle pagination (V2 format)
        limit = tool_parameters.get("limit", 10)
        if limit and 1 <= limit <= 100:
            payload["limit"] = limit

        # Handle similarity threshold
        similarity_threshold = tool_parameters.get("similarity_threshold")
        if similarity_threshold is not None:
            if "filters" not in payload:
                payload["filters"] = {}
            payload["filters"]["similarity_threshold"] = similarity_threshold

        # Handle advanced filters - 合并到主filters字段
        if tool_parameters.get("filters"):
            try:
                additional_filters = json.loads(tool_parameters["filters"])
                # 合并额外的filters
                if "filters" in payload:
                    payload["filters"].update(additional_filters)
                else:
                    payload["filters"] = additional_filters
            except json.JSONDecodeError as e:
                error_message = f"Invalid JSON in filters: {str(e)}"
                yield self.create_json_message({
                    "status": "error",
                    "error": error_message
                })
                yield self.create_text_message(f"Failed to retrieve memory: {error_message}")
                return

        # Criteria-based retrieval support
        if tool_parameters.get("score_range"):
            score_range = tool_parameters["score_range"]
            if "filters" not in payload:
                payload["filters"] = {}
            payload["filters"]["score_range"] = score_range

        if tool_parameters.get("date_range"):
            date_range = tool_parameters["date_range"]
            if "filters" not in payload:
                payload["filters"] = {}
            payload["filters"]["date_range"] = date_range

        if tool_parameters.get("category_filter"):
            category_filter = tool_parameters["category_filter"]
            if "filters" not in payload:
                payload["filters"] = {}
            payload["filters"]["categories"] = category_filter

        # Graph memory support
        if tool_parameters.get("enable_graph"):
            payload["enable_graph"] = tool_parameters["enable_graph"]

        if tool_parameters.get("graph_entities"):
            payload["graph_entities"] = tool_parameters["graph_entities"]

        if tool_parameters.get("relationship_filter"):
            payload["relationship_filter"] = tool_parameters["relationship_filter"]

        # Make HTTP request to mem0 V2 API
        try:
            response = httpx.post(
                f"{api_url}/v2/memories/search/",
                json=payload,
                headers={"Authorization": f"Token {api_key}"},
                timeout=20  # v2检索可能需要更多时间
            )

            response.raise_for_status()
            response_data = response.json()

            # V2 API returns: {"memories": [...], "pagination": {...}}
            memories = []
            pagination_info = {}

            if isinstance(response_data, dict):
                if "memories" in response_data:
                    memories = response_data["memories"]

                if "pagination" in response_data:
                    pagination_info = response_data["pagination"]
            else:
                yield self.create_json_message({
                    "query": query,
                    "api_version": "v2",
                    "memories": [],
                    "error": "Unexpected response format"
                })
                yield self.create_text_message(f"Query: {query}\n\nUnexpected V2 response format")
                return

            # Process V2 memories
            processed_memories = []
            for memory in memories:
                if not isinstance(memory, dict):
                    continue

                # 安全处理score字段，可能为None
                score_value = memory.get("score")
                if score_value is None:
                    score_value = 0.0

                memory_item = {
                    "id": memory.get("id", "unknown"),
                    "memory": memory.get("memory", ""),
                    "score": score_value,
                    "categories": memory.get("categories", []),
                    "created_at": memory.get("created_at", ""),
                    "updated_at": memory.get("updated_at", ""),
                    "metadata": memory.get("metadata", {}),
                    "user_id": memory.get("user_id", ""),
                    "agent_id": memory.get("agent_id", ""),
                    "run_id": memory.get("run_id", "")
                }
                processed_memories.append(memory_item)

            # Return JSON format
            yield self.create_json_message({
                "query": query,
                "api_version": "v2",
                "memories": processed_memories,
                "pagination": pagination_info,
                "total_found": len(processed_memories)
            })

            # Return text format
            text_response = f"Query: {query} (V2 API)\n\n"

            if processed_memories:
                text_response += f"Found {len(processed_memories)} memories:\n"
                for idx, memory in enumerate(processed_memories, 1):
                    text_response += f"\n{idx}. Memory: {memory['memory']}"
                    # 安全处理score字段，可能为None
                    score_value = memory.get('score')
                    if score_value is not None:
                        text_response += f"\n   Score: {score_value:.3f}"
                    else:
                        text_response += f"\n   Score: N/A"
                    if memory['categories']:
                        text_response += f"\n   Categories: {', '.join(memory['categories'])}"
                    if memory['metadata']:
                        text_response += f"\n   Metadata: {memory['metadata']}"
                    text_response += f"\n   Created: {memory['created_at']}"
            else:
                text_response += "No memories found."

            if pagination_info:
                text_response += f"\n\nPagination: {pagination_info}"

            yield self.create_text_message(text_response)

        except httpx.HTTPStatusError as e:
            error_message = f"HTTP error: {e.response.status_code}"
            try:
                error_data = e.response.json()
                if "detail" in error_data:
                    error_message = f"Error: {error_data['detail']}"
                elif "error" in error_data:
                    error_message = f"Error: {error_data['error']}"
            except:
                pass

            yield self.create_json_message({
                "status": "error",
                "api_version": "v2",
                "error": error_message
            })

            yield self.create_text_message(f"Failed to retrieve memory (V2): {error_message}")

        except Exception as e:
            error_message = f"Error: {str(e)}"

            yield self.create_json_message({
                "status": "error",
                "api_version": "v2",
                "error": error_message
            })

            yield self.create_text_message(f"Failed to retrieve memory (V2): {error_message}")
