#!/usr/bin/env python3
"""
Mem0-Dify连接诊断脚本
用于诊断和修复mem0服务连接问题
"""

import httpx
import json
import sys
import time
from typing import Dict, Any, Optional

class Mem0ConnectionDiagnostic:
    def __init__(self, api_url: str = "http://**********:33542", api_key: str = ""):
        self.api_url = api_url.rstrip("/")
        self.api_key = api_key
        self.client = httpx.Client(timeout=30.0)
        
    def test_basic_connectivity(self) -> Dict[str, Any]:
        """测试基本网络连接"""
        print(f"🔍 测试基本连接到: {self.api_url}")
        
        try:
            response = self.client.get(f"{self.api_url}/")
            return {
                "success": True,
                "status_code": response.status_code,
                "response": response.text[:200] if response.text else "Empty response"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    def test_api_endpoints(self) -> Dict[str, Any]:
        """测试各个API端点"""
        endpoints = [
            "/v1/ping/",
            "/v2/ping/", 
            "/v1/memories/",
            "/v2/memories/",
            "/v2/memories/search/",
            "/health/",
            "/docs/",
            "/openapi.json"
        ]
        
        results = {}
        headers = {}
        if self.api_key:
            headers["Authorization"] = f"Token {self.api_key}"
            
        for endpoint in endpoints:
            print(f"🔍 测试端点: {endpoint}")
            try:
                if endpoint in ["/v2/memories/search/"]:
                    # POST请求测试
                    test_payload = {
                        "query": "test",
                        "filters": {"user_id": "diagnostic_test"}
                    }
                    response = self.client.post(
                        f"{self.api_url}{endpoint}",
                        json=test_payload,
                        headers=headers
                    )
                else:
                    # GET请求测试
                    response = self.client.get(
                        f"{self.api_url}{endpoint}",
                        headers=headers
                    )
                
                results[endpoint] = {
                    "success": True,
                    "status_code": response.status_code,
                    "content_type": response.headers.get("content-type", ""),
                    "response_preview": response.text[:200] if response.text else "Empty"
                }
                
                if response.status_code >= 400:
                    try:
                        error_data = response.json()
                        results[endpoint]["error_detail"] = error_data
                    except:
                        results[endpoint]["error_detail"] = response.text
                        
            except Exception as e:
                results[endpoint] = {
                    "success": False,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
                
        return results
    
    def test_search_api_formats(self) -> Dict[str, Any]:
        """测试不同的搜索API格式"""
        print("🔍 测试搜索API的不同格式")
        
        test_cases = [
            {
                "name": "V2 Standard Format",
                "endpoint": "/v2/memories/search/",
                "payload": {
                    "query": "test search",
                    "filters": {"user_id": "test_user"}
                }
            },
            {
                "name": "V2 Minimal Format", 
                "endpoint": "/v2/memories/search/",
                "payload": {
                    "query": "test"
                }
            },
            {
                "name": "V1 Format",
                "endpoint": "/v1/memories/search/",
                "payload": {
                    "query": "test search",
                    "user_id": "test_user"
                }
            }
        ]
        
        results = {}
        headers = {}
        if self.api_key:
            headers["Authorization"] = f"Token {self.api_key}"
            
        for test_case in test_cases:
            name = test_case["name"]
            endpoint = test_case["endpoint"]
            payload = test_case["payload"]
            
            print(f"  📝 测试: {name}")
            try:
                response = self.client.post(
                    f"{self.api_url}{endpoint}",
                    json=payload,
                    headers=headers
                )
                
                results[name] = {
                    "success": True,
                    "status_code": response.status_code,
                    "payload_sent": payload,
                    "response_preview": response.text[:300] if response.text else "Empty"
                }
                
                if response.status_code >= 400:
                    try:
                        error_data = response.json()
                        results[name]["error_detail"] = error_data
                    except:
                        results[name]["error_detail"] = response.text
                        
            except Exception as e:
                results[name] = {
                    "success": False,
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "payload_sent": payload
                }
                
        return results
    
    def analyze_400_error(self, endpoint: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """分析400错误的具体原因"""
        print(f"🔍 分析400错误: {endpoint}")
        
        headers = {"Content-Type": "application/json"}
        if self.api_key:
            headers["Authorization"] = f"Token {self.api_key}"
            
        try:
            response = self.client.post(
                f"{self.api_url}{endpoint}",
                json=payload,
                headers=headers
            )
            
            result = {
                "status_code": response.status_code,
                "headers_sent": headers,
                "payload_sent": payload,
                "response_headers": dict(response.headers),
                "response_text": response.text
            }
            
            if response.status_code == 400:
                try:
                    error_data = response.json()
                    result["parsed_error"] = error_data
                    
                    # 分析常见的400错误原因
                    if "detail" in error_data:
                        result["error_analysis"] = self._analyze_error_detail(error_data["detail"])
                    elif "error" in error_data:
                        result["error_analysis"] = self._analyze_error_detail(error_data["error"])
                        
                except json.JSONDecodeError:
                    result["error_analysis"] = "无法解析错误响应为JSON"
                    
            return result
            
        except Exception as e:
            return {
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    def _analyze_error_detail(self, error_detail: str) -> Dict[str, Any]:
        """分析错误详情"""
        analysis = {
            "possible_causes": [],
            "suggestions": []
        }
        
        error_lower = error_detail.lower()
        
        if "required" in error_lower:
            analysis["possible_causes"].append("缺少必需的字段")
            analysis["suggestions"].append("检查API文档确认所有必需字段")
            
        if "invalid" in error_lower:
            analysis["possible_causes"].append("字段值无效")
            analysis["suggestions"].append("检查字段值的格式和类型")
            
        if "user_id" in error_lower:
            analysis["possible_causes"].append("user_id字段问题")
            analysis["suggestions"].append("确保user_id字段存在且不为空")
            
        if "query" in error_lower:
            analysis["possible_causes"].append("query字段问题")
            analysis["suggestions"].append("确保query字段存在且不为空")
            
        if "filters" in error_lower:
            analysis["possible_causes"].append("filters字段格式问题")
            analysis["suggestions"].append("检查filters字段的JSON格式")
            
        return analysis
    
    def run_full_diagnostic(self) -> Dict[str, Any]:
        """运行完整诊断"""
        print("🚀 开始Mem0连接诊断...")
        print(f"📍 目标服务器: {self.api_url}")
        print(f"🔑 API Key: {'已设置' if self.api_key else '未设置'}")
        print("-" * 50)
        
        results = {
            "server_info": {
                "api_url": self.api_url,
                "api_key_provided": bool(self.api_key)
            },
            "basic_connectivity": self.test_basic_connectivity(),
            "api_endpoints": self.test_api_endpoints(),
            "search_formats": self.test_search_api_formats()
        }
        
        # 如果搜索API返回400，进行详细分析
        search_400_found = False
        for test_name, test_result in results["search_formats"].items():
            if test_result.get("status_code") == 400:
                search_400_found = True
                break
                
        if search_400_found:
            print("\n🔍 检测到400错误，进行详细分析...")
            results["error_analysis"] = self.analyze_400_error(
                "/v2/memories/search/",
                {"query": "test", "filters": {"user_id": "test_user"}}
            )
        
        return results
    
    def print_diagnostic_report(self, results: Dict[str, Any]):
        """打印诊断报告"""
        print("\n" + "="*60)
        print("📊 MEM0连接诊断报告")
        print("="*60)
        
        # 基本连接
        basic = results["basic_connectivity"]
        print(f"\n🌐 基本连接: {'✅ 成功' if basic['success'] else '❌ 失败'}")
        if not basic['success']:
            print(f"   错误: {basic['error']}")
            print(f"   类型: {basic['error_type']}")
        
        # API端点测试
        print(f"\n🔌 API端点测试:")
        for endpoint, result in results["api_endpoints"].items():
            status = "✅" if result['success'] and result.get('status_code', 0) < 400 else "❌"
            status_code = result.get('status_code', 'N/A')
            print(f"   {status} {endpoint} - {status_code}")
            
            if not result['success']:
                print(f"      错误: {result['error']}")
            elif result.get('status_code', 0) >= 400:
                print(f"      详情: {result.get('error_detail', 'No details')}")
        
        # 搜索API格式测试
        print(f"\n🔍 搜索API格式测试:")
        for test_name, result in results["search_formats"].items():
            status = "✅" if result['success'] and result.get('status_code', 0) < 400 else "❌"
            status_code = result.get('status_code', 'N/A')
            print(f"   {status} {test_name} - {status_code}")
            
            if not result['success']:
                print(f"      错误: {result['error']}")
            elif result.get('status_code') == 400:
                print(f"      400错误详情: {result.get('error_detail', 'No details')}")
        
        # 错误分析
        if "error_analysis" in results:
            print(f"\n🔬 400错误详细分析:")
            analysis = results["error_analysis"]
            if "parsed_error" in analysis:
                print(f"   解析的错误: {analysis['parsed_error']}")
            if "error_analysis" in analysis:
                error_analysis = analysis["error_analysis"]
                if error_analysis.get("possible_causes"):
                    print(f"   可能原因: {', '.join(error_analysis['possible_causes'])}")
                if error_analysis.get("suggestions"):
                    print(f"   建议: {', '.join(error_analysis['suggestions'])}")
        
        print("\n" + "="*60)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Mem0-Dify连接诊断工具")
    parser.add_argument("--url", default="http://**********:33542", 
                       help="Mem0 API URL (默认: http://**********:33542)")
    parser.add_argument("--api-key", default="", 
                       help="Mem0 API Key (可选)")
    parser.add_argument("--output", help="输出结果到JSON文件")
    
    args = parser.parse_args()
    
    # 创建诊断器
    diagnostic = Mem0ConnectionDiagnostic(args.url, args.api_key)
    
    # 运行诊断
    results = diagnostic.run_full_diagnostic()
    
    # 打印报告
    diagnostic.print_diagnostic_report(results)
    
    # 保存结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"\n💾 诊断结果已保存到: {args.output}")

if __name__ == "__main__":
    main()
