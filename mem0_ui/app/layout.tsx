import type React from "react";

import "@/app/globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Navbar } from "@/components/Navbar";
import { Toaster } from "@/components/ui/toaster";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MSWProvider } from "@/components/MSWProvider";
import ErrorBoundary from "@/components/common/ErrorBoundary";

import { Providers } from "./providers";

export const metadata = {
  title: "Mem0 - Advanced Memory Management Interface",
  description: "Professional interface for Mem0 core memory management with user analytics, monitoring, and advanced features",
  keywords: "memory management, AI, Mem0, analytics, monitoring, user management",
  authors: [{ name: "Mem0 Team" }],
  robots: "index, follow",
  generator: "v0.dev",
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#00d4aa" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <script src="/runtime-config.js" />
      </head>
      <body className="h-screen font-sans antialiased flex flex-col bg-zinc-950">
        {/* 跳转到主内容的无障碍链接 */}
        <a href="#main-content" className="skip-link">
          Skip to main content
        </a>

        <MSWProvider>
          <Providers>
            <ThemeProvider
              attribute="class"
              defaultTheme="dark"
              enableSystem
              disableTransitionOnChange
            >
              <ErrorBoundary>
                <Navbar />
                <main id="main-content" className="flex-1 overflow-hidden">
                  <ScrollArea className="h-full">
                    <ErrorBoundary resetOnPropsChange>
                      <div className="animate-fade-in">
                        {children}
                      </div>
                    </ErrorBoundary>
                  </ScrollArea>
                </main>
                <Toaster />
              </ErrorBoundary>
            </ThemeProvider>
          </Providers>
        </MSWProvider>
      </body>
    </html>
  );
}
