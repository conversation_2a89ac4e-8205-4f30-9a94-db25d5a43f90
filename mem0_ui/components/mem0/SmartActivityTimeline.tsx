'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Clock, Activity, User, Database, Zap, CheckCircle, Play, RefreshCw, TrendingDown } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ActivityFilter, ActivityFilterConfig, defaultConfig } from './ActivityFilter';
import { filterActivities, calculateActivityStats, ActivityItem } from '@/utils/activityFilter';
import CollapsibleMetadata from './CollapsibleMetadata';

interface SmartActivityTimelineProps {
  dataSource?: 'database' | 'memory_api' | 'local_storage' | 'mock';
  userId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const SmartActivityTimeline: React.FC<SmartActivityTimelineProps> = ({
  dataSource = 'database',
  userId,
  autoRefresh = false,
  refreshInterval = 30000
}) => {
  const [originalActivities, setOriginalActivities] = useState<ActivityItem[]>([]);
  const [filterConfig, setFilterConfig] = useState<ActivityFilterConfig>(defaultConfig);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // 应用过滤器
  const filteredActivities = useMemo(() => {
    return filterActivities(originalActivities, filterConfig);
  }, [originalActivities, filterConfig]);

  // 计算统计信息
  const activityStats = useMemo(() => {
    return calculateActivityStats(originalActivities, filteredActivities);
  }, [originalActivities, filteredActivities]);

  // 获取活动数据
  const fetchActivities = async () => {
    setLoading(true);
    setError(null);

    try {
      let data: ActivityItem[] = [];

      switch (dataSource) {
        case 'database':
          data = await fetchFromDatabase();
          break;
        case 'memory_api':
          data = await fetchFromMemoryAPI();
          break;
        case 'local_storage':
          data = fetchFromLocalStorage();
          break;
        case 'mock':
          data = fetchMockData();
          break;
      }

      setOriginalActivities(data);
      setLastRefresh(new Date());
      
      // 保存到本地存储作为备份
      if (dataSource !== 'local_storage' && data.length > 0) {
        saveToLocalStorage(data);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取活动数据失败';
      setError(errorMessage);
      
      // 尝试从本地存储获取备份数据
      if (dataSource !== 'local_storage') {
        const backupData = fetchFromLocalStorage();
        if (backupData.length > 0) {
          setOriginalActivities(backupData);
          setError(`${errorMessage} (显示缓存数据)`);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  // 从数据库API获取
  const fetchFromDatabase = async (): Promise<ActivityItem[]> => {
    const response = await fetch('http://localhost:8000/v1/activities/?limit=100');
    if (!response.ok) throw new Error('Database API failed');
    const data = await response.json();
    return data.activities || [];
  };

  // 从记忆API推断活动
  const fetchFromMemoryAPI = async (): Promise<ActivityItem[]> => {
    const activities: ActivityItem[] = [];
    
    try {
      const memoryResponse = await fetch('http://localhost:8000/v1/memories/?limit=30');
      if (memoryResponse.ok) {
        const memoryData = await memoryResponse.json();
        
        memoryData.memories?.forEach((memory: any) => {
          activities.push({
            id: `mem_${memory.id}`,
            timestamp: memory.created_at || new Date().toISOString(),
            operation: 'ADD',
            details: `创建记忆: ${memory.memory?.substring(0, 50)}...`,
            user_id: memory.user_id,
            memory_id: memory.id,
            metadata: {
              memory_type: 'user',
              content_length: memory.memory?.length || 0,
              categories: memory.custom_categories || [],
              source: 'memory_api'
            },
            status: 'success'
          });
        });
      }

      // 添加一些模拟的UI操作（这些会被过滤掉）
      activities.push({
        id: `ui_${Date.now()}`,
        timestamp: new Date().toISOString(),
        operation: 'SEARCH',
        details: 'page_load: 自动搜索记忆',
        user_id: 'ui',
        metadata: {
          source: 'ui',
          trigger: 'auto',
          type: 'ui_operation'
        },
        status: 'success'
      });

    } catch (error) {
      console.error('Error fetching from memory API:', error);
    }

    return activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  };

  // 从本地存储获取
  const fetchFromLocalStorage = (): ActivityItem[] => {
    try {
      const stored = localStorage.getItem('mem0_smart_activities');
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error reading from localStorage:', error);
    }
    return [];
  };

  // 保存到本地存储
  const saveToLocalStorage = (activities: ActivityItem[]) => {
    try {
      localStorage.setItem('mem0_smart_activities', JSON.stringify(activities));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  };

  // 生成模拟数据
  const fetchMockData = (): ActivityItem[] => {
    const mockActivities: ActivityItem[] = [];
    const operations: Array<'ADD' | 'SEARCH' | 'UPDATE' | 'DELETE'> = ['ADD', 'SEARCH', 'UPDATE', 'DELETE'];
    
    for (let i = 0; i < 50; i++) {
      const operation = operations[Math.floor(Math.random() * operations.length)];
      const timestamp = new Date(Date.now() - i * 300000).toISOString();
      const isUIOperation = Math.random() < 0.3; // 30%的概率是UI操作
      const hasEmptyMetadata = Math.random() < 0.2; // 20%的概率metadata为空
      
      mockActivities.push({
        id: `mock_${i}`,
        timestamp,
        operation,
        details: isUIOperation 
          ? `ui_load: ${operation}操作` 
          : `用户${operation}操作`,
        user_id: isUIOperation 
          ? 'ui' 
          : `user_${Math.floor(Math.random() * 10)}`,
        memory_id: `memory_${Math.floor(Math.random() * 1000)}`,
        metadata: hasEmptyMetadata ? null : {
          source: isUIOperation ? 'ui' : 'user',
          operation_type: operation.toLowerCase(),
          timestamp: timestamp,
          ...(operation === 'SEARCH' && {
            query_type: 'semantic',
            results_count: Math.floor(Math.random() * 20) + 1,
            trigger: isUIOperation ? 'auto' : 'manual'
          }),
          ...(operation === 'ADD' && {
            memory_type: 'user',
            content_length: Math.floor(Math.random() * 200) + 50,
            embedding_model: 'text-embedding-ada-002'
          })
        },
        response_time: `${Math.floor(Math.random() * 500) + 50}ms`,
        status: 'success'
      });
    }
    
    return mockActivities;
  };

  // 自动刷新
  useEffect(() => {
    fetchActivities();

    if (autoRefresh) {
      const interval = setInterval(fetchActivities, refreshInterval);
      return () => clearInterval(interval);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataSource, autoRefresh, refreshInterval]);

  // 获取操作信息
  const getOperationInfo = (operation: string) => {
    const operationMap = {
      'ADD': { text: '添加', color: 'bg-green-500/20 text-green-400 border-green-500/30' },
      'SEARCH': { text: '搜索', color: 'bg-blue-500/20 text-blue-400 border-blue-500/30' },
      'UPDATE': { text: '更新', color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' },
      'DELETE': { text: '删除', color: 'bg-red-500/20 text-red-400 border-red-500/30' }
    };
    return operationMap[operation as keyof typeof operationMap] || { text: operation, color: 'bg-zinc-500/20 text-zinc-400 border-zinc-500/30' };
  };

  // 格式化时间戳
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('zh-CN', { 
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return timestamp;
    }
  };

  return (
    <div className="space-y-6">
      {/* 过滤器 */}
      <ActivityFilter
        config={filterConfig}
        onConfigChange={setFilterConfig}
        activityStats={activityStats}
      />

      {/* 活动时间线 */}
      <Card className="bg-zinc-900 border-zinc-800">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Activity className="w-5 h-5 text-[#00d4aa]" />
              智能活动时间线
              <span className="text-xs text-zinc-500 font-normal ml-2">
                数据源: {dataSource}
              </span>
            </CardTitle>
            
            <div className="flex items-center gap-2">
              {activityStats.filtered < activityStats.total && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <TrendingDown className="w-3 h-3" />
                  已过滤 {activityStats.total - activityStats.filtered} 条
                </Badge>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={fetchActivities}
                disabled={loading}
                className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
              >
                <RefreshCw className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
            </div>
          </div>
          
          {error && (
            <div className="text-red-400 text-sm mt-2">
              {error}
            </div>
          )}
        </CardHeader>
        
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-zinc-800">
                  <th className="text-left p-3 text-sm font-medium text-zinc-400">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      时间
                    </div>
                  </th>
                  <th className="text-left p-3 text-sm font-medium text-zinc-400">
                    <div className="flex items-center gap-2">
                      <Play className="w-4 h-4" />
                      操作
                    </div>
                  </th>
                  <th className="text-left p-3 text-sm font-medium text-zinc-400">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      用户ID
                    </div>
                  </th>
                  <th className="text-left p-3 text-sm font-medium text-zinc-400">
                    <div className="flex items-center gap-2">
                      <Database className="w-4 h-4" />
                      元数据
                    </div>
                  </th>
                  <th className="text-left p-3 text-sm font-medium text-zinc-400">
                    <div className="flex items-center gap-2">
                      <Zap className="w-4 h-4" />
                      响应时间
                    </div>
                  </th>
                  <th className="text-left p-3 text-sm font-medium text-zinc-400">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4" />
                      状态
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-zinc-800">
                {filteredActivities.map((item) => {
                  const operationInfo = getOperationInfo(item.operation);
                  return (
                    <tr key={item.id} className="hover:bg-zinc-800/30 transition-colors">
                      <td className="p-3 text-sm text-zinc-300 font-mono">
                        {formatTimestamp(item.timestamp)}
                      </td>
                      <td className="p-3">
                        <Badge className={`${operationInfo.color} border text-xs`}>
                          {operationInfo.text}
                        </Badge>
                      </td>
                      <td className="p-3 text-sm text-zinc-300 font-mono">
                        {item.user_id ? (
                          <span className="text-[#00d4aa]">{item.user_id}</span>
                        ) : (
                          <span className="text-zinc-500">-</span>
                        )}
                      </td>
                      <td className="p-3 text-sm text-zinc-300 max-w-xs">
                        <div className="max-w-xs">
                          <CollapsibleMetadata metadata={item.metadata} maxPreviewLength={40} />
                        </div>
                      </td>
                      <td className="p-3 text-sm text-zinc-300 font-mono">
                        <span className={item.response_time ? 'text-[#00d4aa]' : 'text-zinc-500'}>
                          {item.response_time || '-'}
                        </span>
                      </td>
                      <td className="p-3">
                        <Badge className={`${item.status === 'success' ? 'bg-green-500/20 text-green-400 border-green-500/30' : 'bg-red-500/20 text-red-400 border-red-500/30'} border text-xs`}>
                          {item.status}
                        </Badge>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
            
            {filteredActivities.length === 0 && !loading && (
              <div className="text-center py-8 text-zinc-500">
                {originalActivities.length === 0 
                  ? "没有找到活动数据" 
                  : "所有活动都被过滤器隐藏了，请调整过滤条件"
                }
              </div>
            )}
            
            {loading && (
              <div className="text-center py-8 text-zinc-500">
                正在加载活动数据...
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SmartActivityTimeline;
