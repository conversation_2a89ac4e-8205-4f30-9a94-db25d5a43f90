import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Copy, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface CollapsibleMetadataProps {
  metadata: any;
  maxPreviewLength?: number;
}

const CollapsibleMetadata: React.FC<CollapsibleMetadataProps> = ({
  metadata,
  maxPreviewLength = 50
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [copied, setCopied] = useState(false);

  // 如果没有metadata，显示占位符
  if (!metadata) {
    return <span className="text-xs text-zinc-500">无数据</span>;
  }

  // 如果metadata是空对象，也显示占位符
  if (typeof metadata === 'object' && Object.keys(metadata).length === 0) {
    return <span className="text-xs text-zinc-500">空对象</span>;
  }

  const jsonString = JSON.stringify(metadata, null, 2);
  const previewString = JSON.stringify(metadata, null, 0);
  const shouldCollapse = previewString.length > maxPreviewLength;

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(jsonString);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  if (!shouldCollapse) {
    // 如果数据较短，直接显示
    return (
      <div className="flex items-center gap-1.5">
        <code className="text-xs text-zinc-400 bg-zinc-800/50 px-1.5 py-0.5 rounded text-[11px]">
          {previewString}
        </code>
        <Button
          variant="ghost"
          size="sm"
          className="h-5 w-5 p-0 hover:bg-zinc-700"
          onClick={handleCopy}
        >
          {copied ? (
            <Check className="w-2.5 h-2.5 text-green-400" />
          ) : (
            <Copy className="w-2.5 h-2.5 text-zinc-400" />
          )}
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* 预览行 */}
      <div
        className="flex items-center gap-1.5 cursor-pointer hover:bg-zinc-800/30 rounded p-0.5 -m-0.5"
        onClick={toggleExpanded}
      >
        <Button
          variant="ghost"
          size="sm"
          className="h-4 w-4 p-0 hover:bg-transparent"
        >
          {isExpanded ? (
            <ChevronDown className="w-2.5 h-2.5 text-zinc-400" />
          ) : (
            <ChevronRight className="w-2.5 h-2.5 text-zinc-400" />
          )}
        </Button>
        <code className="text-xs text-zinc-400 bg-zinc-800/50 px-1.5 py-0.5 rounded flex-1 text-[11px]">
          {previewString.substring(0, maxPreviewLength)}...
        </code>
        <Button
          variant="ghost"
          size="sm"
          className="h-5 w-5 p-0 hover:bg-zinc-700"
          onClick={handleCopy}
        >
          {copied ? (
            <Check className="w-2.5 h-2.5 text-green-400" />
          ) : (
            <Copy className="w-2.5 h-2.5 text-zinc-400" />
          )}
        </Button>
      </div>

      {/* 展开的完整内容 */}
      {isExpanded && (
        <div className="mt-2 p-2.5 bg-zinc-800/30 rounded border border-zinc-700">
          <pre className="text-xs text-zinc-300 whitespace-pre-wrap overflow-x-auto max-h-48 overflow-y-auto text-[11px]">
            {jsonString}
          </pre>
        </div>
      )}
    </div>
  );
};

export default CollapsibleMetadata;
