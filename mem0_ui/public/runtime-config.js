// 运行时配置 - 动态设置API基础URL
(function() {
  if (typeof window !== 'undefined') {
    // 设置全局配置
    window.MEM0_RUNTIME_CONFIG = {
      apiBaseUrl: window.location.origin + '/api/proxy',
      isRemote: window.location.hostname !== 'localhost'
    };
    
    console.log('🔧 [Runtime Config] API Base URL:', window.MEM0_RUNTIME_CONFIG.apiBaseUrl);
    console.log('🔧 [Runtime Config] Is Remote:', window.MEM0_RUNTIME_CONFIG.isRemote);
  }
})();