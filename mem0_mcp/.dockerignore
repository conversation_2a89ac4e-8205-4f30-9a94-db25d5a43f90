# Docker ignore file for MCP server
# Exclude unnecessary files from build context

# Development and testing files
tests/
pytest.ini
*.pyc
__pycache__/
*.pyo
*.pyd
.Python
*.so
.pytest_cache/

# Documentation and examples
docs/
examples/
README.md
*.md

# Git and version control
.git/
.gitignore
.gitattributes

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs and data (runtime directories)
logs/
data/
*.log

# Environment and configuration
.env
.env.*
!.env.template

# Build artifacts
dist/
build/
*.egg-info/

# Backup files
*.bak
*.backup
backup/

# Deployment scripts (keep deploy-mcp-only.sh)
deploy.sh
deploy_remote.sh

# Docker files from other configurations
docker-compose.*.yml
!docker-compose.yml