"""
Graph database management tools for Mem0 MCP server
"""

import json
from typing import Any, Dict, List, Optional

from .base_tool import <PERSON>Tool, ToolResult
from ..client.adapters import BaseAdapter
from ..utils.errors import ToolExecutionError
from ..utils.logger import get_logger

logger = get_logger(__name__)


class GraphEntityTool(BaseTool):
    """Tool for managing graph entities"""
    
    def __init__(self, adapter: BaseAdapter):
        super().__init__(
            name="manage_graph_entities",
            description="Create, read, update, and delete graph entities"
        )
        self.adapter = adapter
    
    async def execute(self, arguments: Dict[str, Any]) -> ToolResult:
        """Execute graph entity management operations"""
        try:
            await self.validate_arguments(arguments)
            
            # Get user identity from context or arguments
            identity = self.get_user_identity(arguments)
            
            # Extract required parameters
            operation = arguments["operation"]
            
            # Build parameters for API call using resolved identity
            params = {}
            if identity.user_id:
                params["user_id"] = identity.user_id
            if identity.agent_id:
                params["agent_id"] = identity.agent_id
            if identity.run_id:
                params["run_id"] = identity.run_id
            
            self.logger.debug(f"Graph entity operation: {operation} for identity: {identity.get_primary_id()}")
            
            # Execute operation based on type
            if operation == "create":
                result = await self._create_entity(arguments, params)
            elif operation == "get":
                result = await self._get_entities(arguments, params)
            elif operation == "update":
                result = await self._update_entity(arguments, params)
            elif operation == "delete":
                result = await self._delete_entity(arguments, params)
            else:
                return ToolResult.error(f"Unknown operation: {operation}")
            
            return result
                
        except Exception as e:
            self.logger.error(f"Error in graph entity tool: {str(e)}")
            return ToolResult.error(str(e))
    
    async def _create_entity(self, arguments: Dict[str, Any], params: Dict[str, Any]) -> ToolResult:
        """Create a new graph entity"""
        entity_data = arguments["entity_data"]
        
        # Add entity data to parameters
        create_params = {**params, **entity_data}
        
        response = await self.adapter.create_graph_entity(**create_params)
        
        if isinstance(response, dict):
            if response.get("success"):
                entity = response.get("entity", {})
                result_text = f"Successfully created graph entity"
                if "id" in entity:
                    result_text += f" with ID: {entity['id']}"
                if "name" in entity:
                    result_text += f"\nEntity name: {entity['name']}"
                if "type" in entity:
                    result_text += f"\nEntity type: {entity['type']}"
                
                content = [
                    {"type": "text", "text": result_text},
                    {"type": "text", "text": f"Full response: {json.dumps(response, indent=2)}"}
                ]
                return ToolResult(content=content)
            else:
                error_msg = response.get("error", "Unknown error occurred")
                return ToolResult.error(f"Failed to create entity: {error_msg}")
        else:
            return ToolResult.error(f"Unexpected response format: {type(response)}")
    
    async def _get_entities(self, arguments: Dict[str, Any], params: Dict[str, Any]) -> ToolResult:
        """Get graph entities using memory API with graph enabled"""
        # Use memory search with graph enabled instead of direct graph endpoints
        # since graph data is stored within the memory system
        search_params = {**params, "enable_graph": True, "output_format": "v1.1"}
        
        # Search for all memories to get graph relations
        response = await self.adapter.get_memories(**search_params)
        
        # Extract entities from relations if available
        entities = []
        if isinstance(response, dict):
            relations = response.get("relations", [])
            if relations:
                # Extract unique entities from relations
                entity_set = set()
                for relation in relations:
                    if "source" in relation:
                        entity_set.add((relation["source"], relation.get("source_type", "unknown")))
                    if "target" in relation:
                        entity_set.add((relation["target"], relation.get("target_type", "unknown")))
                
                # Convert to entity format
                for i, (name, entity_type) in enumerate(entity_set):
                    entities.append({
                        "id": f"entity_{i}",
                        "name": name,
                        "type": entity_type,
                        "properties": {}
                    })
        
        if not entities:
            return ToolResult.success("No graph entities found. Note: Graph entities are extracted from memories with enable_graph=true.")
        
        # Apply filters if provided
        filters = arguments.get("filters", {})
        if filters.get("entity_type"):
            entities = [e for e in entities if e["type"] == filters["entity_type"]]
        
        # Apply limit
        limit = arguments.get("limit", 100)
        entities = entities[:limit]
        
        # Format entities for display
        result_lines = [f"Found {len(entities)} graph entities:"]
        result_lines.append("(Extracted from graph-enabled memories)")
        
        for i, entity in enumerate(entities[:10], 1):  # Limit to first 10
            entity_id = entity.get("id", "unknown")
            entity_name = entity.get("name", "")
            entity_type = entity.get("type", "")
            
            result_lines.append(f"\n{i}. ID: {entity_id}")
            result_lines.append(f"   Name: {entity_name}")
            result_lines.append(f"   Type: {entity_type}")
        
        if len(entities) > 10:
            result_lines.append(f"\n... and {len(entities) - 10} more entities")
        
        content = [
            {"type": "text", "text": "\n".join(result_lines)},
            {"type": "text", "text": f"Full response: {json.dumps(response, indent=2)}"}
        ]
        return ToolResult(content=content)
    
    async def _update_entity(self, arguments: Dict[str, Any], params: Dict[str, Any]) -> ToolResult:
        """Update an existing graph entity"""
        entity_id = arguments["entity_id"]
        update_data = arguments["update_data"]
        
        response = await self.adapter.update_graph_entity(entity_id=entity_id, data=update_data, **params)
        
        if isinstance(response, dict):
            if response.get("success"):
                return ToolResult.success(f"Successfully updated entity {entity_id}")
            else:
                error_msg = response.get("error", "Unknown error occurred")
                return ToolResult.error(f"Failed to update entity: {error_msg}")
        else:
            return ToolResult.success(f"Successfully updated entity {entity_id}")
    
    async def _delete_entity(self, arguments: Dict[str, Any], params: Dict[str, Any]) -> ToolResult:
        """Delete a graph entity"""
        entity_id = arguments["entity_id"]
        
        response = await self.adapter.delete_graph_entity(entity_id=entity_id, **params)
        
        if isinstance(response, dict):
            if response.get("success"):
                return ToolResult.success(f"Successfully deleted entity {entity_id}")
            else:
                error_msg = response.get("error", "Unknown error occurred")
                return ToolResult.error(f"Failed to delete entity: {error_msg}")
        elif isinstance(response, bool) and response:
            return ToolResult.success(f"Successfully deleted entity {entity_id}")
        else:
            return ToolResult.error(f"Failed to delete entity {entity_id}")
    
    def get_input_schema(self) -> Dict[str, Any]:
        """Get input schema for graph entity management"""
        from ..protocol.message_types import GRAPH_ENTITY_SCHEMA
        return GRAPH_ENTITY_SCHEMA


class GraphRelationshipTool(BaseTool):
    """Tool for managing graph relationships"""
    
    def __init__(self, adapter: BaseAdapter):
        super().__init__(
            name="manage_graph_relationships",
            description="Create, read, update, and delete graph relationships"
        )
        self.adapter = adapter
    
    async def execute(self, arguments: Dict[str, Any]) -> ToolResult:
        """Execute graph relationship management operations"""
        try:
            await self.validate_arguments(arguments)
            
            # Get user identity from context or arguments
            identity = self.get_user_identity(arguments)
            
            # Extract required parameters
            operation = arguments["operation"]
            
            # Build parameters for API call using resolved identity
            params = {}
            if identity.user_id:
                params["user_id"] = identity.user_id
            if identity.agent_id:
                params["agent_id"] = identity.agent_id
            if identity.run_id:
                params["run_id"] = identity.run_id
            
            self.logger.debug(f"Graph relationship operation: {operation} for identity: {identity.get_primary_id()}")
            
            # Execute operation based on type
            if operation == "create":
                result = await self._create_relationship(arguments, params)
            elif operation == "get":
                result = await self._get_relationships(arguments, params)
            elif operation == "update":
                result = await self._update_relationship(arguments, params)
            elif operation == "delete":
                result = await self._delete_relationship(arguments, params)
            else:
                return ToolResult.error(f"Unknown operation: {operation}")
            
            return result
                
        except Exception as e:
            self.logger.error(f"Error in graph relationship tool: {str(e)}")
            return ToolResult.error(str(e))
    
    async def _create_relationship(self, arguments: Dict[str, Any], params: Dict[str, Any]) -> ToolResult:
        """Create a new graph relationship"""
        relationship_data = arguments["relationship_data"]
        
        # Add relationship data to parameters
        create_params = {**params, **relationship_data}
        
        response = await self.adapter.create_graph_relationship(**create_params)
        
        if isinstance(response, dict):
            if response.get("success"):
                relationship = response.get("relationship", {})
                result_text = f"Successfully created graph relationship"
                if "id" in relationship:
                    result_text += f" with ID: {relationship['id']}"
                if "source" in relationship and "target" in relationship:
                    result_text += f"\nRelationship: {relationship['source']} -> {relationship['target']}"
                if "type" in relationship:
                    result_text += f"\nType: {relationship['type']}"
                
                content = [
                    {"type": "text", "text": result_text},
                    {"type": "text", "text": f"Full response: {json.dumps(response, indent=2)}"}
                ]
                return ToolResult(content=content)
            else:
                error_msg = response.get("error", "Unknown error occurred")
                return ToolResult.error(f"Failed to create relationship: {error_msg}")
        else:
            return ToolResult.error(f"Unexpected response format: {type(response)}")
    
    async def _get_relationships(self, arguments: Dict[str, Any], params: Dict[str, Any]) -> ToolResult:
        """Get graph relationships using memory API with graph enabled"""
        # Use memory search with graph enabled instead of direct graph endpoints
        # since graph data is stored within the memory system
        search_params = {**params, "enable_graph": True, "output_format": "v1.1"}
        
        # Search for all memories to get graph relations
        response = await self.adapter.get_memories(**search_params)
        
        # Extract relationships from relations if available
        relationships = []
        if isinstance(response, dict):
            relations = response.get("relations", [])
            if relations:
                # Convert relations to relationship format
                for i, relation in enumerate(relations):
                    relationships.append({
                        "id": f"relationship_{i}",
                        "source": relation.get("source", ""),
                        "target": relation.get("target", ""),
                        "type": relation.get("relationship", ""),
                        "weight": relation.get("score", 1.0),
                        "properties": {
                            "source_type": relation.get("source_type", "unknown"),
                            "target_type": relation.get("target_type", "unknown")
                        }
                    })
        
        if not relationships:
            return ToolResult.success("No graph relationships found. Note: Graph relationships are extracted from memories with enable_graph=true.")
        
        # Apply filters if provided
        filters = arguments.get("filters", {})
        if filters.get("source_entity"):
            relationships = [r for r in relationships if r["source"] == filters["source_entity"]]
        if filters.get("target_entity"):
            relationships = [r for r in relationships if r["target"] == filters["target_entity"]]
        if filters.get("relationship_type"):
            relationships = [r for r in relationships if r["type"] == filters["relationship_type"]]
        
        # Apply limit
        limit = arguments.get("limit", 100)
        relationships = relationships[:limit]
        
        # Format relationships for display
        result_lines = [f"Found {len(relationships)} graph relationships:"]
        result_lines.append("(Extracted from graph-enabled memories)")
        
        for i, rel in enumerate(relationships[:10], 1):  # Limit to first 10
            rel_id = rel.get("id", "unknown")
            source = rel.get("source", "")
            target = rel.get("target", "")
            rel_type = rel.get("type", "")
            
            result_lines.append(f"\n{i}. ID: {rel_id}")
            result_lines.append(f"   Relationship: {source} -{rel_type}-> {target}")
            
            # Show weight if available
            if "weight" in rel:
                result_lines.append(f"   Weight: {rel['weight']}")
            
            # Show properties if available
            if "properties" in rel and rel["properties"]:
                props = [f"{k}:{v}" for k, v in rel["properties"].items()][:2]  # Show first 2 properties
                result_lines.append(f"   Properties: {', '.join(props)}")
        
        if len(relationships) > 10:
            result_lines.append(f"\n... and {len(relationships) - 10} more relationships")
        
        content = [
            {"type": "text", "text": "\n".join(result_lines)},
            {"type": "text", "text": f"Full response: {json.dumps(response, indent=2)}"}
        ]
        return ToolResult(content=content)
    
    async def _update_relationship(self, arguments: Dict[str, Any], params: Dict[str, Any]) -> ToolResult:
        """Update an existing graph relationship"""
        relationship_id = arguments["relationship_id"]
        update_data = arguments["update_data"]
        
        response = await self.adapter.update_graph_relationship(relationship_id=relationship_id, data=update_data, **params)
        
        if isinstance(response, dict):
            if response.get("success"):
                return ToolResult.success(f"Successfully updated relationship {relationship_id}")
            else:
                error_msg = response.get("error", "Unknown error occurred")
                return ToolResult.error(f"Failed to update relationship: {error_msg}")
        else:
            return ToolResult.success(f"Successfully updated relationship {relationship_id}")
    
    async def _delete_relationship(self, arguments: Dict[str, Any], params: Dict[str, Any]) -> ToolResult:
        """Delete a graph relationship"""
        relationship_id = arguments["relationship_id"]
        
        response = await self.adapter.delete_graph_relationship(relationship_id=relationship_id, **params)
        
        if isinstance(response, dict):
            if response.get("success"):
                return ToolResult.success(f"Successfully deleted relationship {relationship_id}")
            else:
                error_msg = response.get("error", "Unknown error occurred")
                return ToolResult.error(f"Failed to delete relationship: {error_msg}")
        elif isinstance(response, bool) and response:
            return ToolResult.success(f"Successfully deleted relationship {relationship_id}")
        else:
            return ToolResult.error(f"Failed to delete relationship {relationship_id}")
    
    def get_input_schema(self) -> Dict[str, Any]:
        """Get input schema for graph relationship management"""
        from ..protocol.message_types import GRAPH_RELATIONSHIP_SCHEMA
        return GRAPH_RELATIONSHIP_SCHEMA

