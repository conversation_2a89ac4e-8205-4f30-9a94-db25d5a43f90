#!/bin/bash

# MCP Server Deployment Script
# Simplified deployment for MCP service only

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    echo "MCP Server Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start       Start the MCP server"
    echo "  stop        Stop the MCP server"
    echo "  restart     Restart the MCP server"
    echo "  status      Show server status"
    echo "  logs        Show server logs"
    echo "  logs-f      Follow server logs"
    echo "  build       Build the MCP server image"
    echo "  clean       Clean up containers and images"
    echo "  setup       Initial setup (create directories and .env)"
    echo "  health      Check server health"
    echo "  help        Show this help message"
    echo ""
    echo "Options:"
    echo "  -d, --detach    Run in detached mode (for start command)"
    echo "  -f, --force     Force operation (for stop/clean commands)"
    echo ""
    echo "Environment Variables (can be set in .env file):"
    echo "  MEM0_BASE_URL              External Mem0 API URL (required)"
    echo "  MEM0_API_VERSION           Mem0 API version (default: v1)"
    echo "  MEM0_API_KEY               Mem0 API key (if required)"
    echo "  MCP_LOG_LEVEL              Log level (default: INFO)"
    echo ""
    echo "Examples:"
    echo "  $0 setup                   # Initial setup"
    echo "  $0 start -d                # Start in detached mode"
    echo "  $0 logs -f                 # Follow logs"
    echo "  $0 status                  # Check status"
}

# Check if Docker and Docker Compose are installed
check_dependencies() {
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    # Check for Docker Compose (modern or legacy)
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE="docker compose"
    elif command -v $DOCKER_COMPOSE &> /dev/null; then
        DOCKER_COMPOSE="$DOCKER_COMPOSE"
    else
        error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
}

# Setup function
setup() {
    log "Setting up MCP server environment..."
    
    # Create necessary directories with correct permissions
    mkdir -p logs data
    
    # Set proper ownership for Docker user (UID 1000)
    if [[ "$EUID" -eq 0 ]]; then
        # Running as root, set ownership to UID 1000
        chown -R 1000:1000 logs data
    else
        # Running as regular user, check if we need sudo
        if ! touch logs/test.log 2>/dev/null; then
            warn "Cannot write to logs directory. You may need to run with sudo or fix permissions manually:"
            warn "  sudo chown -R 1000:1000 $(pwd)/logs $(pwd)/data"
        else
            rm -f logs/test.log
        fi
    fi
    
    # Set directory permissions
    chmod 755 logs data
    
    # Copy environment template if .env doesn't exist
    if [[ ! -f .env ]]; then
        if [[ -f .env.template ]]; then
            cp .env.template .env
            log "Created .env file from template"
            warn "Please edit .env file to configure your Mem0 API endpoint"
        else
            warn ".env.template not found. Please create .env file manually"
        fi
    else
        log ".env file already exists"
    fi
    
    log "Setup completed!"
    log "Next steps:"
    log "1. Edit .env file to configure your Mem0 API endpoint"
    log "2. Run: $0 start"
}

# Start function
start_server() {
    local detach_flag=""
    if [[ "$1" == "-d" || "$1" == "--detach" ]]; then
        detach_flag="-d"
    fi
    
    log "Starting MCP server..."
    
    # Check if .env exists
    if [[ ! -f .env ]]; then
        warn ".env file not found. Using default environment variables."
        warn "Consider running: $0 setup"
    fi
    
    $DOCKER_COMPOSE up $detach_flag
    
    if [[ -n "$detach_flag" ]]; then
        log "MCP server started in detached mode"
        log "Check status with: $0 status"
        log "View logs with: $0 logs"
    fi
}

# Stop function
stop_server() {
    log "Stopping MCP server..."
    $DOCKER_COMPOSE down
    log "MCP server stopped"
}

# Status function
show_status() {
    log "MCP Server Status:"
    $DOCKER_COMPOSE ps
    
    echo ""
    log "Container logs (last 10 lines):"
    $DOCKER_COMPOSE logs --tail=10 mem0-mcp-server 2>/dev/null || warn "Container not running or logs not available"
}

# Logs function
show_logs() {
    local follow_flag=""
    if [[ "$1" == "-f" || "$1" == "--follow" ]]; then
        follow_flag="-f"
    fi
    
    $DOCKER_COMPOSE logs $follow_flag mem0-mcp-server
}

# Build function
build_image() {
    log "Building MCP server image..."
    $DOCKER_COMPOSE build
    log "Build completed"
}

# Clean function
clean_up() {
    local force_flag=""
    if [[ "$1" == "-f" || "$1" == "--force" ]]; then
        force_flag="--force"
    fi
    
    log "Cleaning up MCP server..."
    $DOCKER_COMPOSE down --volumes --remove-orphans
    
    if [[ -n "$force_flag" ]]; then
        log "Removing images..."
        $DOCKER_COMPOSE down --rmi all
    fi
    
    log "Cleanup completed"
}

# Health check function
health_check() {
    log "Checking MCP server health..."
    
    # Check if container is running
    if ! $DOCKER_COMPOSE ps | grep -q "mem0-mcp-server.*Up"; then
        error "MCP server container is not running"
        return 1
    fi
    
    # Check health endpoint
    if curl -f -s "http://localhost:8001/health" > /dev/null; then
        log "✅ MCP server is healthy"
        return 0
    else
        error "❌ MCP server health check failed"
        return 1
    fi
}

# Main command processing
main() {
    check_dependencies
    
    case "${1:-help}" in
        "start")
            start_server "$2"
            ;;
        "stop")
            stop_server
            ;;
        "restart")
            stop_server
            start_server "-d"
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs
            ;;
        "logs-f")
            show_logs "-f"
            ;;
        "build")
            build_image
            ;;
        "clean")
            clean_up "$2"
            ;;
        "setup")
            setup
            ;;
        "health")
            health_check
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"